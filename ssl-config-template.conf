# OpenSSL Configuration Template for Multi-Domain SSL Certificates
# 
# Usage:
# 1. Copy this file: cp ssl-config-template.conf nginx/ssl/domains.conf
# 2. Edit the [alt_names] section with your domains
# 3. Run: openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
#           -keyout nginx/ssl/server.key \
#           -out nginx/ssl/server.crt \
#           -config nginx/ssl/domains.conf \
#           -extensions v3_req

[req]
distinguished_name = req_distinguished_name
req_extensions = v3_req
prompt = no

[req_distinguished_name]
CN = localhost

[v3_req]
keyUsage = keyEncipherment, dataEncipherment
extendedKeyUsage = serverAuth
subjectAltName = @alt_names

[alt_names]
# Add your domains here:
DNS.1 = localhost
DNS.2 = example.local
DNS.3 = www.example.local
DNS.4 = api.example.local
DNS.5 = admin.example.local
# Add more domains as needed...
IP.1 = 127.0.0.1
IP.2 = ::1
