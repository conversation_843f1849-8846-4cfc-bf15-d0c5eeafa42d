# Nginx HTTPS Template

Simple nginx template with HTTPS support using self-signed certificates.

## 🚀 Quick Start

### Prerequisites
- Docker & Docker Compose
- OpenSSL

### 1. Generate SSL Certificate

```bash
# Copy config template
cp ssl-config-template.conf nginx/ssl/domains.conf

# Edit nginx/ssl/domains.conf - add your domains to [alt_names] section

# Generate certificate
mkdir -p nginx/ssl
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
    -keyout nginx/ssl/server.key \
    -out nginx/ssl/server.crt \
    -config nginx/ssl/domains.conf \
    -extensions v3_req
```

### 2. Update Nginx Configuration

Edit `nginx/conf.d/default.conf` and update server_name with your domains:

```nginx
server_name localhost your-domain.local api.your-domain.local;
```

### 3. Start Server

```bash
docker-compose up -d
```

### 4. Access Your Site

- **HTTPS:** https://localhost
- **HTTP:** http://localhost (redirects to HTTPS)

**Note:** <PERSON><PERSON><PERSON> will show security warning for self-signed certificates.

## 📁 Project Structure

```
├── docker-compose.yml          # Docker services configuration
├── ssl-config-template.conf    # OpenSSL config template
├── nginx/
│   ├── nginx.conf             # Main nginx configuration
│   ├── conf.d/
│   │   └── default.conf       # Server block configuration
│   └── ssl/                   # SSL certificates (generated)
│       ├── domains.conf       # OpenSSL config (copy from template)
│       ├── server.crt         # SSL certificate
│       └── server.key         # Private key
└── www/
    ├── index.html             # Main page
    └── 404.html               # Error page
```

## 🔧 Configuration

### Adding Domains

1. **Edit SSL config:**
   ```bash
   # Edit nginx/ssl/domains.conf
   [alt_names]
   DNS.1 = localhost
   DNS.2 = myapp.local
   DNS.3 = www.myapp.local
   DNS.4 = api.myapp.local
   DNS.5 = admin.myapp.local
   IP.1 = 127.0.0.1
   IP.2 = ::1
   ```

2. **Update nginx config:**
   ```nginx
   # Edit nginx/conf.d/default.conf
   server_name localhost myapp.local api.myapp.local admin.myapp.local;
   ```

3. **Regenerate certificate:**
   ```bash
   openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
       -keyout nginx/ssl/server.key \
       -out nginx/ssl/server.crt \
       -config nginx/ssl/domains.conf \
       -extensions v3_req

   docker-compose restart nginx
   ```

## 🔒 Certificate Details

- **Type:** Self-signed with SAN support
- **Validity:** 365 days
- **Key Size:** 2048 bits
- **TLS:** 1.2 & 1.3 support

## 🐳 Docker Commands

```bash
# Start
docker-compose up -d

# Restart nginx after config changes
docker-compose restart nginx

# View logs
docker-compose logs nginx

# Stop
docker-compose down
```

## 🔍 Verify Certificate

```bash
# Check certificate domains
openssl x509 -in nginx/ssl/server.crt -text -noout | grep -A 5 "Subject Alternative Name"

# Test nginx config
docker-compose exec nginx nginx -t
```

## 📝 Notes

- Add domains to your hosts file: `127.0.0.1 your-domain.local`
- Browser will show security warning for self-signed certificates
- For production, replace with CA-signed certificates
