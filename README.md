# Nginx HTTPS Template

A production-ready nginx template with HTTPS support and self-signed certificate generation. Perfect for development, testing, and as a starting point for production deployments.

## 🚀 Quick Start

### Prerequisites
- Docker & Docker Compose
- OpenSSL (for certificate generation)

### 1. Generate SSL Certificates

**Using Scripts (Recommended):**
```bash
# Linux/macOS/WSL
./generate-ssl.sh app.local api.local admin.local

# Windows
generate-ssl.bat app.local api.local admin.local
```

**Manual with OpenSSL:**
```bash
# One-liner approach
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
    -keyout nginx/ssl/server.key \
    -out nginx/ssl/server.crt \
    -subj "/CN=app.local" \
    -addext "subjectAltName=DNS:app.local,DNS:api.local,DNS:admin.local,DNS:localhost,IP:127.0.0.1"
```

### 2. Update Domain Configuration

Edit `nginx/conf.d/default.conf` and replace the server_name with your domains:

```nginx
server_name your-domain.local api.your-domain.local admin.your-domain.local;
```

### 3. Start the Server

```bash
docker-compose up -d
```

### 4. Access Your Site

- **HTTPS:** https://localhost (or your domain)
- **HTTP:** http://localhost (redirects to HTTPS)

**Note:** You'll see a browser security warning for self-signed certificates - this is normal.

## 🚀 Quick Examples

### Complete Setup (One Command)
```bash
# Linux/macOS/WSL
./setup.sh app.local api.local admin.local

# Windows
setup.bat app.local api.local admin.local
```

### Manual Steps
```bash
# 1. Generate certificate
./generate-ssl.sh app.local api.local admin.local

# 2. Update nginx config (optional - scripts do this automatically)
# Edit nginx/conf.d/default.conf: server_name app.local api.local admin.local;

# 3. Start containers
docker-compose up -d
```

### OpenSSL Direct Commands
See [SIMPLE-SSL.md](SIMPLE-SSL.md) for manual OpenSSL commands and config file approach.

## 📁 Project Structure

```
├── docker-compose.yml          # Docker services configuration
├── generate-ssl.sh            # SSL certificate generator (Linux/macOS)
├── generate-ssl.bat           # SSL certificate generator (Windows)
├── nginx/
│   ├── nginx.conf             # Main nginx configuration
│   ├── conf.d/
│   │   └── default.conf       # Server block configuration
│   └── ssl/                   # SSL certificates (generated)
│       ├── server.crt         # SSL certificate
│       └── server.key         # Private key
└── www/
    ├── index.html             # Main page
    └── 404.html               # Error page
```

## 🔧 Customization

### For New Projects

1. **Change Domain(s):**
   ```bash
   # Single domain
   ./generate-ssl.sh mydomain.local

   # Multiple domains
   ./generate-ssl.sh app.local api.local admin.local

   # Update nginx/conf.d/default.conf
   server_name mydomain.local;  # or multiple: app.local api.local admin.local;
   ```

2. **Update Content:**
   - Replace files in `www/` directory
   - Modify `www/index.html` for your landing page

3. **Advanced Configuration:**
   - Edit `nginx/nginx.conf` for global settings
   - Modify `nginx/conf.d/default.conf` for server-specific settings

### Multiple Domains

**Option 1: Single Certificate for Multiple Domains (Recommended)**
```bash
# Generate one certificate for all domains
./generate-ssl.sh app.local api.local admin.local

# Update server_name in nginx/conf.d/default.conf
server_name app.local api.local admin.local;
```

**Option 2: Separate Certificates (Advanced)**
```bash
# Generate certificates for each domain
./generate-ssl.sh domain1.local
./generate-ssl.sh domain2.local

# Create separate config files
cp nginx/conf.d/default.conf nginx/conf.d/domain1.conf
cp nginx/conf.d/default.conf nginx/conf.d/domain2.conf

# Edit each config file with appropriate domain and SSL paths
```

## 🔒 SSL Configuration

### Certificate Details
- **Type:** Self-signed
- **Validity:** 365 days
- **Key Size:** 2048 bits
- **Supported:** TLS 1.2 & 1.3
- **SAN Support:** Yes (includes www subdomain and localhost)

### Security Features
- HTTP to HTTPS redirect
- HSTS headers
- Security headers (XSS, CSRF, etc.)
- Modern SSL ciphers
- HTTP/2 support

## 🐳 Docker Commands

```bash
# Start services
docker-compose up -d

# View logs
docker-compose logs -f nginx

# Restart nginx
docker-compose restart nginx

# Stop services
docker-compose down

# Rebuild and start
docker-compose up -d --build
```

## 🔍 Troubleshooting

### Common Issues

1. **Certificate Errors:**
   ```bash
   # Regenerate certificates
   ./generate-ssl.sh your-domain
   docker-compose restart nginx
   ```

2. **Permission Issues:**
   ```bash
   # Fix SSL directory permissions
   chmod 755 nginx/ssl
   chmod 600 nginx/ssl/server.key
   chmod 644 nginx/ssl/server.crt
   ```

3. **Domain Not Resolving:**
   Add to your hosts file:
   ```
   127.0.0.1 your-domain.local
   ```

4. **Port Conflicts:**
   Edit `docker-compose.yml` to use different ports:
   ```yaml
   ports:
     - "8080:80"
     - "8443:443"
   ```

### Logs and Debugging

```bash
# Check nginx configuration
docker-compose exec nginx nginx -t

# View error logs
docker-compose logs nginx

# Access container shell
docker-compose exec nginx sh
```

## 📋 Features

- ✅ **HTTPS by default** with automatic HTTP redirect
- ✅ **Self-signed certificates** with multi-domain SAN support
- ✅ **Modern SSL/TLS** configuration (TLS 1.2/1.3)
- ✅ **Security headers** (HSTS, XSS protection, etc.)
- ✅ **HTTP/2** support
- ✅ **Gzip compression** for better performance
- ✅ **Static file caching** with proper headers
- ✅ **Custom error pages** (404, 50x)
- ✅ **Docker-based** for easy deployment
- ✅ **Cross-platform** certificate generation scripts

## 🔄 Production Considerations

For production use:

1. **Replace self-signed certificates** with CA-signed certificates
2. **Update security headers** for your specific needs
3. **Configure proper logging** and monitoring
4. **Set up certificate renewal** (if using Let's Encrypt)
5. **Review and harden** nginx configuration
6. **Implement proper backup** strategies

## 📝 License

This template is provided as-is for educational and development purposes.
