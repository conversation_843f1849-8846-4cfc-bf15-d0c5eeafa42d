#!/bin/bash

# Simple setup script
# Usage: ./setup.sh [domain1] [domain2] [domain3] ...

DOMAINS=("$@")
if [ ${#DOMAINS[@]} -eq 0 ]; then
    DOMAINS=("localhost")
fi

echo "🚀 Quick setup for: ${DOMAINS[*]}"

# Generate SSL certificate
echo "🔐 Generating SSL certificate..."
./generate-ssl.sh "${DOMAINS[@]}"

# Update nginx config
if [ "${DOMAINS[0]}" != "localhost" ]; then
    echo "⚙️ Updating nginx config..."
    SERVER_NAMES="${DOMAINS[*]}"
    sed -i.bak "s/server_name localhost app.local api.local admin.local;/server_name $SERVER_NAMES;/g" nginx/conf.d/default.conf
fi

# Start containers
echo "🐳 Starting containers..."
docker-compose up -d

echo "✅ Setup complete!"
echo "🌐 Access: https://${DOMAINS[0]}"

# Hosts file reminder
for domain in "${DOMAINS[@]}"; do
    if [ "$domain" != "localhost" ] && [ "$domain" != "127.0.0.1" ]; then
        echo "💡 Add to hosts file: 127.0.0.1 $domain"
    fi
done
