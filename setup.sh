#!/bin/bash

# Quick setup script for nginx HTTPS template
# Usage: ./setup.sh [domain-name]

set -e

DOMAIN=${1:-"localhost"}
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

echo -e "${BLUE}🚀 Setting up nginx HTTPS template for domain: ${DOMAIN}${NC}"
echo ""

# Step 1: Generate SSL certificates
echo -e "${YELLOW}Step 1: Generating SSL certificates...${NC}"
./generate-ssl.sh "$DOMAIN"

# Step 2: Update nginx configuration
echo -e "${YELLOW}Step 2: Updating nginx configuration...${NC}"
if [ "$DOMAIN" != "localhost" ]; then
    # Update server_name in nginx config
    sed -i.bak "s/server_name localhost example.local;/server_name $DOMAIN;/g" nginx/conf.d/default.conf
    echo "Updated server_name to: $DOMAIN"
fi

# Step 3: Start Docker containers
echo -e "${YELLOW}Step 3: Starting Docker containers...${NC}"
docker-compose up -d

# Step 4: Wait for nginx to start
echo -e "${YELLOW}Step 4: Waiting for nginx to start...${NC}"
sleep 3

# Step 5: Test the setup
echo -e "${YELLOW}Step 5: Testing the setup...${NC}"
if curl -k -s -o /dev/null -w "%{http_code}" https://localhost | grep -q "200"; then
    echo -e "${GREEN}✅ HTTPS server is running successfully!${NC}"
else
    echo -e "${RED}❌ HTTPS server test failed${NC}"
    exit 1
fi

echo ""
echo -e "${GREEN}🎉 Setup complete!${NC}"
echo -e "${BLUE}Access your site at:${NC}"
echo -e "  🔒 HTTPS: https://$DOMAIN"
echo -e "  🔄 HTTP:  http://$DOMAIN (redirects to HTTPS)"
echo ""
echo -e "${YELLOW}Note: You'll see a browser security warning for self-signed certificates.${NC}"

# Add to hosts file reminder
if [ "$DOMAIN" != "localhost" ] && [ "$DOMAIN" != "127.0.0.1" ]; then
    echo ""
    echo -e "${YELLOW}💡 Don't forget to add this to your hosts file:${NC}"
    echo -e "   127.0.0.1 $DOMAIN"
fi
