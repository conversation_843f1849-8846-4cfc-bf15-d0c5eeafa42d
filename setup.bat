@echo off
setlocal enabledelayedexpansion

REM Simple setup script
REM Usage: setup.bat [domain1] [domain2] [domain3] ...

set DOMAINS=%*
if "%DOMAINS%"=="" set DOMAINS=localhost

echo 🚀 Quick setup for: %DOMAINS%

REM Generate SSL certificate
echo 🔐 Generating SSL certificate...
call generate-ssl.bat %DOMAINS%
if errorlevel 1 goto error

REM Update nginx config
for %%d in (%DOMAINS%) do (
    if not "%%d"=="localhost" (
        echo ⚙️ Updating nginx config...
        copy nginx\conf.d\default.conf nginx\conf.d\default.conf.bak >nul
        powershell -Command "(Get-Content nginx\conf.d\default.conf) -replace 'server_name localhost app.local api.local admin.local;', 'server_name %DOMAINS%;' | Set-Content nginx\conf.d\default.conf"
        goto done_config
    )
)
:done_config

REM Start containers
echo 🐳 Starting containers...
docker-compose up -d
if errorlevel 1 goto error

echo ✅ Setup complete!
for %%d in (%DOMAINS%) do (
    echo 🌐 Access: https://%%d
    goto done_access
)
:done_access

REM Hosts file reminder
for %%d in (%DOMAINS%) do (
    if not "%%d"=="localhost" if not "%%d"=="127.0.0.1" (
        echo 💡 Add to hosts file: 127.0.0.1 %%d
    )
)

pause
exit /b 0

:error
echo ❌ Setup failed!
pause
exit /b 1
