@echo off
setlocal enabledelayedexpansion

REM Quick setup script for nginx HTTPS template
REM Usage: setup.bat [domain-name]

set DOMAIN=%1
if "%DOMAIN%"=="" set DOMAIN=localhost

echo 🚀 Setting up nginx HTTPS template for domain: %DOMAIN%
echo.

REM Step 1: Generate SSL certificates
echo Step 1: Generating SSL certificates...
call generate-ssl.bat %DOMAIN%
if errorlevel 1 goto error

REM Step 2: Update nginx configuration
echo Step 2: Updating nginx configuration...
if not "%DOMAIN%"=="localhost" (
    REM Create a backup and update server_name in nginx config
    copy nginx\conf.d\default.conf nginx\conf.d\default.conf.bak >nul
    powershell -Command "(Get-Content nginx\conf.d\default.conf) -replace 'server_name localhost example.local;', 'server_name %DOMAIN%;' | Set-Content nginx\conf.d\default.conf"
    echo Updated server_name to: %DOMAIN%
)

REM Step 3: Start Docker containers
echo Step 3: Starting Docker containers...
docker-compose up -d
if errorlevel 1 goto error

REM Step 4: Wait for nginx to start
echo Step 4: Waiting for nginx to start...
timeout /t 3 /nobreak >nul

REM Step 5: Test the setup
echo Step 5: Testing the setup...
curl -k -s -o nul -w "%%{http_code}" https://localhost | findstr "200" >nul
if errorlevel 1 (
    echo ❌ HTTPS server test failed
    goto error
)

echo.
echo 🎉 Setup complete!
echo Access your site at:
echo   🔒 HTTPS: https://%DOMAIN%
echo   🔄 HTTP:  http://%DOMAIN% (redirects to HTTPS)
echo.
echo Note: You'll see a browser security warning for self-signed certificates.

REM Add to hosts file reminder
if not "%DOMAIN%"=="localhost" if not "%DOMAIN%"=="127.0.0.1" (
    echo.
    echo 💡 Don't forget to add this to your hosts file:
    echo    127.0.0.1 %DOMAIN%
)

pause
exit /b 0

:error
echo.
echo ❌ Setup failed!
pause
exit /b 1
