# Simple SSL Certificate Generation

## Method 1: Using Scripts (Recommended)

**Single domain:**
```bash
./generate-ssl.sh myapp.local
```

**Multiple domains:**
```bash
./generate-ssl.sh app.local api.local admin.local
```

## Method 2: Manual with OpenSSL Config File

### Step 1: Create config file
```bash
mkdir -p nginx/ssl
cp ssl-config-template.conf nginx/ssl/domains.conf
```

### Step 2: Edit domains
Edit `nginx/ssl/domains.conf` and update the `[alt_names]` section:
```ini
[alt_names]
DNS.1 = localhost
DNS.2 = myapp.local
DNS.3 = www.myapp.local
DNS.4 = api.myapp.local
DNS.5 = admin.myapp.local
IP.1 = 127.0.0.1
IP.2 = ::1
```

### Step 3: Generate certificate
```bash
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
    -keyout nginx/ssl/server.key \
    -out nginx/ssl/server.crt \
    -config nginx/ssl/domains.conf \
    -extensions v3_req
```

## Method 3: One-liner for Quick Testing

**Linux/macOS/WSL:**
```bash
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
    -keyout nginx/ssl/server.key \
    -out nginx/ssl/server.crt \
    -subj "/CN=app.local" \
    -addext "subjectAltName=DNS:app.local,DNS:api.local,DNS:localhost,IP:127.0.0.1"
```

**Windows (use scripts instead - one-liner has path issues in Git Bash):**
```cmd
generate-ssl.bat app.local api.local
```

## Verify Certificate

```bash
# Check certificate details
openssl x509 -in nginx/ssl/server.crt -text -noout

# Check SAN (Subject Alternative Names)
openssl x509 -in nginx/ssl/server.crt -text -noout | grep -A 5 "Subject Alternative Name"
```

## Start Server

```bash
docker-compose up -d
```

## Files Generated

- `nginx/ssl/server.key` - Private key
- `nginx/ssl/server.crt` - Certificate
- `nginx/ssl/domains.conf` - OpenSSL config (if using Method 2)
