#!/bin/bash

# SSL Certificate Generation Script for Nginx
# This script generates self-signed SSL certificates

set -e

# Configuration
DOMAIN=${1:-"localhost"}
COUNTRY="US"
STATE="State"
CITY="City"
ORGANIZATION="Organization"
ORGANIZATIONAL_UNIT="IT Department"
EMAIL="admin@${DOMAIN}"

# Directories
SSL_DIR="./nginx/ssl"
DAYS=365

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== SSL Certificate Generation Script ===${NC}"
echo -e "${YELLOW}Domain: ${DOMAIN}${NC}"
echo ""

# Create SSL directory if it doesn't exist
if [ ! -d "$SSL_DIR" ]; then
    echo -e "${YELLOW}Creating SSL directory...${NC}"
    mkdir -p "$SSL_DIR"
fi

# Check if certificates already exist
if [ -f "$SSL_DIR/server.crt" ] && [ -f "$SSL_DIR/server.key" ]; then
    echo -e "${YELLOW}SSL certificates already exist!${NC}"
    read -p "Do you want to regenerate them? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo -e "${GREEN}Keeping existing certificates.${NC}"
        exit 0
    fi
    echo -e "${YELLOW}Regenerating certificates...${NC}"
fi

# Generate private key
echo -e "${BLUE}Generating private key...${NC}"
openssl genrsa -out "$SSL_DIR/server.key" 2048

# Generate certificate signing request
echo -e "${BLUE}Generating certificate signing request...${NC}"
openssl req -new -key "$SSL_DIR/server.key" -out "$SSL_DIR/server.csr" -subj "//C=$COUNTRY\ST=$STATE\L=$CITY\O=$ORGANIZATION\OU=$ORGANIZATIONAL_UNIT\CN=$DOMAIN\emailAddress=$EMAIL"

# Generate self-signed certificate
echo -e "${BLUE}Generating self-signed certificate...${NC}"
openssl x509 -req -days $DAYS -in "$SSL_DIR/server.csr" -signkey "$SSL_DIR/server.key" -out "$SSL_DIR/server.crt" -extensions v3_req -extfile <(
cat <<EOF
[v3_req]
keyUsage = keyEncipherment, dataEncipherment
extendedKeyUsage = serverAuth
subjectAltName = @alt_names

[alt_names]
DNS.1 = $DOMAIN
DNS.2 = www.$DOMAIN
DNS.3 = localhost
IP.1 = 127.0.0.1
IP.2 = ::1
EOF
)

# Set proper permissions
chmod 600 "$SSL_DIR/server.key"
chmod 644 "$SSL_DIR/server.crt"

# Clean up CSR file
rm "$SSL_DIR/server.csr"

echo ""
echo -e "${GREEN}✅ SSL certificates generated successfully!${NC}"
echo -e "${BLUE}Certificate details:${NC}"
echo -e "  📁 Location: $SSL_DIR"
echo -e "  🔑 Private Key: server.key"
echo -e "  📜 Certificate: server.crt"
echo -e "  📅 Valid for: $DAYS days"
echo -e "  🌐 Domain: $DOMAIN"
echo ""
echo -e "${YELLOW}Next steps:${NC}"
echo -e "  1. Update nginx/conf.d/default.conf with your domain name"
echo -e "  2. Run: docker-compose up -d"
echo -e "  3. Access: https://$DOMAIN (accept security warning for self-signed cert)"
echo ""
echo -e "${RED}⚠️  Note: This is a self-signed certificate. Browsers will show a security warning.${NC}"
