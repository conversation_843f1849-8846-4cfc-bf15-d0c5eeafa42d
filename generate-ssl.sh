#!/bin/bash

# Simple SSL Certificate Generator
# Usage: ./generate-ssl.sh [domain1] [domain2] [domain3] ...

DOMAINS=("$@")
if [ ${#DOMAINS[@]} -eq 0 ]; then
    DOMAINS=("localhost")
fi

SSL_DIR="./nginx/ssl"
mkdir -p "$SSL_DIR"

echo "🔐 Generating SSL certificate for: ${DOMAINS[*]}"

# Create domains config file
cat > "$SSL_DIR/domains.conf" << EOF
[req]
distinguished_name = req_distinguished_name
req_extensions = v3_req
prompt = no

[req_distinguished_name]
CN = ${DOMAINS[0]}

[v3_req]
keyUsage = keyEncipherment, dataEncipherment
extendedKeyUsage = serverAuth
subjectAltName = @alt_names

[alt_names]
EOF

# Add all domains to config
DNS_COUNT=1
for domain in "${DOMAINS[@]}"; do
    echo "DNS.$DNS_COUNT = $domain" >> "$SSL_DIR/domains.conf"
    ((DNS_COUNT++))
    # Add www subdomain (except for localhost/IPs)
    if [[ "$domain" != "localhost" && "$domain" != "127.0.0.1" && ! "$domain" =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
        echo "DNS.$DNS_COUNT = www.$domain" >> "$SSL_DIR/domains.conf"
        ((DNS_COUNT++))
    fi
done

# Always add localhost
echo "DNS.$DNS_COUNT = localhost" >> "$SSL_DIR/domains.conf"
echo "IP.1 = 127.0.0.1" >> "$SSL_DIR/domains.conf"
echo "IP.2 = ::1" >> "$SSL_DIR/domains.conf"

# Generate certificate
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
    -keyout "$SSL_DIR/server.key" \
    -out "$SSL_DIR/server.crt" \
    -config "$SSL_DIR/domains.conf" \
    -extensions v3_req

echo "✅ Certificate generated successfully!"
echo "📁 Files: $SSL_DIR/server.key, $SSL_DIR/server.crt"
