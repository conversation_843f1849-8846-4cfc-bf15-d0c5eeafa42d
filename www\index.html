<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HTTPS Nginx Template</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }
        
        .container {
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            padding: 3rem;
            border-radius: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            max-width: 600px;
        }
        
        h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
            background: linear-gradient(45deg, #fff, #f0f0f0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .subtitle {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }
        
        .ssl-info {
            background: rgba(0, 0, 0, 0.2);
            padding: 1.5rem;
            border-radius: 10px;
            margin: 2rem 0;
        }
        
        .ssl-status {
            font-size: 1.1rem;
            margin-bottom: 1rem;
        }
        
        .ssl-icon {
            font-size: 2rem;
            margin-bottom: 1rem;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 2rem;
        }
        
        .info-card {
            background: rgba(255, 255, 255, 0.1);
            padding: 1rem;
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .info-card h3 {
            margin-bottom: 0.5rem;
            color: #fff;
        }
        
        .info-card p {
            opacity: 0.8;
            font-size: 0.9rem;
        }
        
        @media (max-width: 768px) {
            .container {
                margin: 1rem;
                padding: 2rem;
            }
            
            h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔒 HTTPS Ready</h1>
        <p class="subtitle">Nginx SSL Template Successfully Deployed</p>
        
        <div class="ssl-info">
            <div class="ssl-icon">🛡️</div>
            <div class="ssl-status">
                <strong>SSL Status:</strong> <span id="ssl-status">Checking...</span>
            </div>
            <div>
                <strong>Protocol:</strong> <span id="protocol">Loading...</span>
            </div>
        </div>
        
        <div class="info-grid">
            <div class="info-card">
                <h3>🚀 Server</h3>
                <p>Nginx with Alpine Linux</p>
            </div>
            <div class="info-card">
                <h3>🔐 Encryption</h3>
                <p>TLS 1.2 & 1.3 Support</p>
            </div>
            <div class="info-card">
                <h3>📱 Responsive</h3>
                <p>Mobile-First Design</p>
            </div>
            <div class="info-card">
                <h3>⚡ Performance</h3>
                <p>HTTP/2 & Gzip Enabled</p>
            </div>
        </div>
    </div>

    <script>
        // Check SSL status
        document.addEventListener('DOMContentLoaded', function() {
            const sslStatus = document.getElementById('ssl-status');
            const protocol = document.getElementById('protocol');
            
            if (location.protocol === 'https:') {
                sslStatus.textContent = 'Active ✅';
                sslStatus.style.color = '#4ade80';
                protocol.textContent = 'HTTPS';
            } else {
                sslStatus.textContent = 'Not Active ❌';
                sslStatus.style.color = '#f87171';
                protocol.textContent = 'HTTP';
            }
        });
    </script>
</body>
</html>
