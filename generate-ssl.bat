@echo off
setlocal enabledelayedexpansion

REM SSL Certificate Generation Script for Nginx (Windows)
REM This script generates self-signed SSL certificates using OpenSSL

echo === SSL Certificate Generation Script ===
echo.

REM Configuration
set DOMAIN=%1
if "%DOMAIN%"=="" set DOMAIN=localhost
set COUNTRY=US
set STATE=State
set CITY=City
set ORGANIZATION=Organization
set ORGANIZATIONAL_UNIT=IT Department
set EMAIL=admin@%DOMAIN%

REM Directories
set SSL_DIR=.\nginx\ssl
set DAYS=365

echo Domain: %DOMAIN%
echo.

REM Check if OpenSSL is available
openssl version >nul 2>&1
if errorlevel 1 (
    echo ERROR: OpenSSL is not installed or not in PATH
    echo Please install OpenSSL or use WSL/Git Bash to run generate-ssl.sh
    pause
    exit /b 1
)

REM Create SSL directory if it doesn't exist
if not exist "%SSL_DIR%" (
    echo Creating SSL directory...
    mkdir "%SSL_DIR%"
)

REM Check if certificates already exist
if exist "%SSL_DIR%\server.crt" if exist "%SSL_DIR%\server.key" (
    echo SSL certificates already exist!
    set /p REGENERATE="Do you want to regenerate them? (y/N): "
    if /i not "!REGENERATE!"=="y" (
        echo Keeping existing certificates.
        pause
        exit /b 0
    )
    echo Regenerating certificates...
)

REM Generate private key
echo Generating private key...
openssl genrsa -out "%SSL_DIR%\server.key" 2048
if errorlevel 1 goto error

REM Generate certificate signing request
echo Generating certificate signing request...
openssl req -new -key "%SSL_DIR%\server.key" -out "%SSL_DIR%\server.csr" -subj "/C=%COUNTRY%/ST=%STATE%/L=%CITY%/O=%ORGANIZATION%/OU=%ORGANIZATIONAL_UNIT%/CN=%DOMAIN%/emailAddress=%EMAIL%"
if errorlevel 1 goto error

REM Create temporary config file for SAN
echo [v3_req] > "%SSL_DIR%\temp.conf"
echo keyUsage = keyEncipherment, dataEncipherment >> "%SSL_DIR%\temp.conf"
echo extendedKeyUsage = serverAuth >> "%SSL_DIR%\temp.conf"
echo subjectAltName = @alt_names >> "%SSL_DIR%\temp.conf"
echo. >> "%SSL_DIR%\temp.conf"
echo [alt_names] >> "%SSL_DIR%\temp.conf"
echo DNS.1 = %DOMAIN% >> "%SSL_DIR%\temp.conf"
echo DNS.2 = www.%DOMAIN% >> "%SSL_DIR%\temp.conf"
echo DNS.3 = localhost >> "%SSL_DIR%\temp.conf"
echo IP.1 = 127.0.0.1 >> "%SSL_DIR%\temp.conf"
echo IP.2 = ::1 >> "%SSL_DIR%\temp.conf"

REM Generate self-signed certificate
echo Generating self-signed certificate...
openssl x509 -req -days %DAYS% -in "%SSL_DIR%\server.csr" -signkey "%SSL_DIR%\server.key" -out "%SSL_DIR%\server.crt" -extensions v3_req -extfile "%SSL_DIR%\temp.conf"
if errorlevel 1 goto error

REM Clean up temporary files
del "%SSL_DIR%\server.csr"
del "%SSL_DIR%\temp.conf"

echo.
echo ✅ SSL certificates generated successfully!
echo Certificate details:
echo   📁 Location: %SSL_DIR%
echo   🔑 Private Key: server.key
echo   📜 Certificate: server.crt
echo   📅 Valid for: %DAYS% days
echo   🌐 Domain: %DOMAIN%
echo.
echo Next steps:
echo   1. Update nginx/conf.d/default.conf with your domain name
echo   2. Run: docker-compose up -d
echo   3. Access: https://%DOMAIN% (accept security warning for self-signed cert)
echo.
echo ⚠️  Note: This is a self-signed certificate. Browsers will show a security warning.
pause
exit /b 0

:error
echo.
echo ❌ Error occurred during certificate generation!
pause
exit /b 1
