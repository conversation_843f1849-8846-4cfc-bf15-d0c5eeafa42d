@echo off
setlocal enabledelayedexpansion

REM Simple SSL Certificate Generator (Windows)
REM Usage: generate-ssl.bat [domain1] [domain2] [domain3] ...

set DOMAINS=%*
if "%DOMAINS%"=="" set DOMAINS=localhost

set SSL_DIR=.\nginx\ssl
if not exist "%SSL_DIR%" mkdir "%SSL_DIR%"

echo 🔐 Generating SSL certificate for: %DOMAINS%

REM Create domains config file
echo [req] > "%SSL_DIR%\domains.conf"
echo distinguished_name = req_distinguished_name >> "%SSL_DIR%\domains.conf"
echo req_extensions = v3_req >> "%SSL_DIR%\domains.conf"
echo prompt = no >> "%SSL_DIR%\domains.conf"
echo. >> "%SSL_DIR%\domains.conf"
echo [req_distinguished_name] >> "%SSL_DIR%\domains.conf"

REM Get first domain for CN
for %%d in (%DOMAINS%) do (
    echo CN = %%d >> "%SSL_DIR%\domains.conf"
    goto done_cn
)
:done_cn

echo. >> "%SSL_DIR%\domains.conf"
echo [v3_req] >> "%SSL_DIR%\domains.conf"
echo keyUsage = keyEncipherment, dataEncipherment >> "%SSL_DIR%\domains.conf"
echo extendedKeyUsage = serverAuth >> "%SSL_DIR%\domains.conf"
echo subjectAltName = @alt_names >> "%SSL_DIR%\domains.conf"
echo. >> "%SSL_DIR%\domains.conf"
echo [alt_names] >> "%SSL_DIR%\domains.conf"

REM Add all domains
set DNS_COUNT=1
for %%d in (%DOMAINS%) do (
    echo DNS.!DNS_COUNT! = %%d >> "%SSL_DIR%\domains.conf"
    set /a DNS_COUNT+=1
    REM Add www subdomain (except for localhost/IPs)
    if not "%%d"=="localhost" if not "%%d"=="127.0.0.1" (
        echo DNS.!DNS_COUNT! = www.%%d >> "%SSL_DIR%\domains.conf"
        set /a DNS_COUNT+=1
    )
)

REM Always add localhost
echo DNS.!DNS_COUNT! = localhost >> "%SSL_DIR%\domains.conf"
echo IP.1 = 127.0.0.1 >> "%SSL_DIR%\domains.conf"
echo IP.2 = ::1 >> "%SSL_DIR%\domains.conf"

REM Generate certificate
openssl req -x509 -nodes -days 365 -newkey rsa:2048 -keyout "%SSL_DIR%\server.key" -out "%SSL_DIR%\server.crt" -config "%SSL_DIR%\domains.conf" -extensions v3_req

if errorlevel 1 (
    echo ❌ Error: OpenSSL failed. Make sure OpenSSL is installed.
    pause
    exit /b 1
)

echo ✅ Certificate generated successfully!
echo 📁 Files: %SSL_DIR%\server.key, %SSL_DIR%\server.crt
pause
